package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region7326Spawns : NPCSpawnsScript() {
    init {
        RAT_2854(1800, 10126, 0, SOUTH, 14)
        RAT_2854(1800, 10171, 0, SOUTH, 14)
        RAT_2854(1804, 10140, 0, SOUTH, 14)
        RAT_2854(1811, 10159, 0, SOUTH, 14)
        RAT_2854(1812, 10122, 0, SOUTH, 14)
    }
}