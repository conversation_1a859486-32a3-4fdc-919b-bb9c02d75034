package com.near_reality.plugins.spawns

import com.near_reality.scripts.npc.spawns.NPCSpawnsScript
import com.zenyte.game.world.entity.npc.NpcId
import com.zenyte.game.world.entity.npc.NpcId.*
import com.zenyte.game.util.Direction.*

class Region8267Spawns : NPCSpawnsScript() {
    init {
        COSMIC_BEING(2090, 4829, 0, SOUT<PERSON>, 5)
        1836(2058, 4828, 2, SOUT<PERSON>, 5)
        1837(2065, 4834, 2, SOUTH, 5)
        1836(2099, 4833, 2, SOUTH, 5)
    }
}